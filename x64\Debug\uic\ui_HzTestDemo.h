/********************************************************************************
** Form generated from reading UI file 'HzTestDemo.ui'
**
** Created by: Qt User Interface Compiler version 5.6.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_HZTESTDEMO_H
#define UI_HZTESTDEMO_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_HzTestDemoClass
{
public:
    QGridLayout *gridLayout_2;
    QVBoxLayout *verticalLayout_3;
    QTabWidget *stateTable;
    QWidget *videotab;
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout;
    QPushButton *videoStartBtn;
    QPushButton *videoStopBtn;
    QWidget *coltable;
    QVBoxLayout *verticalLayout_2;
    QGridLayout *gridLayout;
    QLabel *label_2;
    QDoubleSpinBox *ddcBwDsb;
    QSpinBox *attSb;
    QLabel *label_3;
    QLabel *label_6;
    QComboBox *chTypeCmb;
    QComboBox *onOffCmb;
    QLabel *label_5;
    QLabel *label_4;
    QDoubleSpinBox *sigFreqHzDsb;
    QPushButton *collectBtn_2;
    QTextBrowser *userinfotext;

    void setupUi(QDialog *HzTestDemoClass)
    {
        if (HzTestDemoClass->objectName().isEmpty())
            HzTestDemoClass->setObjectName(QStringLiteral("HzTestDemoClass"));
        HzTestDemoClass->resize(651, 390);
        gridLayout_2 = new QGridLayout(HzTestDemoClass);
        gridLayout_2->setSpacing(6);
        gridLayout_2->setContentsMargins(11, 11, 11, 11);
        gridLayout_2->setObjectName(QStringLiteral("gridLayout_2"));
        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setSpacing(6);
        verticalLayout_3->setObjectName(QStringLiteral("verticalLayout_3"));
        stateTable = new QTabWidget(HzTestDemoClass);
        stateTable->setObjectName(QStringLiteral("stateTable"));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(stateTable->sizePolicy().hasHeightForWidth());
        stateTable->setSizePolicy(sizePolicy);
        videotab = new QWidget();
        videotab->setObjectName(QStringLiteral("videotab"));
        verticalLayout = new QVBoxLayout(videotab);
        verticalLayout->setSpacing(6);
        verticalLayout->setContentsMargins(11, 11, 11, 11);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setSpacing(6);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        videoStartBtn = new QPushButton(videotab);
        videoStartBtn->setObjectName(QStringLiteral("videoStartBtn"));

        horizontalLayout->addWidget(videoStartBtn);

        videoStopBtn = new QPushButton(videotab);
        videoStopBtn->setObjectName(QStringLiteral("videoStopBtn"));

        horizontalLayout->addWidget(videoStopBtn);


        verticalLayout->addLayout(horizontalLayout);

        stateTable->addTab(videotab, QString());
        coltable = new QWidget();
        coltable->setObjectName(QStringLiteral("coltable"));
        verticalLayout_2 = new QVBoxLayout(coltable);
        verticalLayout_2->setSpacing(6);
        verticalLayout_2->setContentsMargins(11, 11, 11, 11);
        verticalLayout_2->setObjectName(QStringLiteral("verticalLayout_2"));
        gridLayout = new QGridLayout();
        gridLayout->setSpacing(6);
        gridLayout->setObjectName(QStringLiteral("gridLayout"));
        label_2 = new QLabel(coltable);
        label_2->setObjectName(QStringLiteral("label_2"));

        gridLayout->addWidget(label_2, 0, 0, 1, 1);

        ddcBwDsb = new QDoubleSpinBox(coltable);
        ddcBwDsb->setObjectName(QStringLiteral("ddcBwDsb"));
        ddcBwDsb->setDecimals(1);
        ddcBwDsb->setMinimum(1);
        ddcBwDsb->setMaximum(320);

        gridLayout->addWidget(ddcBwDsb, 1, 4, 1, 1);

        attSb = new QSpinBox(coltable);
        attSb->setObjectName(QStringLiteral("attSb"));
        attSb->setMinimum(0);

        gridLayout->addWidget(attSb, 1, 1, 1, 2);

        label_3 = new QLabel(coltable);
        label_3->setObjectName(QStringLiteral("label_3"));

        gridLayout->addWidget(label_3, 0, 3, 1, 1);

        label_6 = new QLabel(coltable);
        label_6->setObjectName(QStringLiteral("label_6"));

        gridLayout->addWidget(label_6, 1, 0, 1, 1);

        chTypeCmb = new QComboBox(coltable);
        chTypeCmb->setObjectName(QStringLiteral("chTypeCmb"));

        gridLayout->addWidget(chTypeCmb, 0, 1, 1, 2);

        onOffCmb = new QComboBox(coltable);
        onOffCmb->setObjectName(QStringLiteral("onOffCmb"));

        gridLayout->addWidget(onOffCmb, 0, 4, 1, 1);

        label_5 = new QLabel(coltable);
        label_5->setObjectName(QStringLiteral("label_5"));

        gridLayout->addWidget(label_5, 1, 3, 1, 1);

        label_4 = new QLabel(coltable);
        label_4->setObjectName(QStringLiteral("label_4"));

        gridLayout->addWidget(label_4, 0, 5, 1, 1);

        sigFreqHzDsb = new QDoubleSpinBox(coltable);
        sigFreqHzDsb->setObjectName(QStringLiteral("sigFreqHzDsb"));
        sigFreqHzDsb->setDecimals(1);
        sigFreqHzDsb->setMinimum(3);
        sigFreqHzDsb->setMaximum(6000);

        gridLayout->addWidget(sigFreqHzDsb, 0, 6, 1, 1);

        collectBtn_2 = new QPushButton(coltable);
        collectBtn_2->setObjectName(QStringLiteral("collectBtn_2"));

        gridLayout->addWidget(collectBtn_2, 1, 6, 1, 1);


        verticalLayout_2->addLayout(gridLayout);

        stateTable->addTab(coltable, QString());

        verticalLayout_3->addWidget(stateTable);

        userinfotext = new QTextBrowser(HzTestDemoClass);
        userinfotext->setObjectName(QStringLiteral("userinfotext"));

        verticalLayout_3->addWidget(userinfotext);


        gridLayout_2->addLayout(verticalLayout_3, 0, 0, 1, 1);


        retranslateUi(HzTestDemoClass);

        stateTable->setCurrentIndex(1);


        QMetaObject::connectSlotsByName(HzTestDemoClass);
    } // setupUi

    void retranslateUi(QDialog *HzTestDemoClass)
    {
        HzTestDemoClass->setWindowTitle(QApplication::translate("HzTestDemoClass", "HzTestDemo", 0));
        videoStartBtn->setText(QApplication::translate("HzTestDemoClass", "\345\274\200\345\247\213\351\207\207\351\233\206", 0));
        videoStopBtn->setText(QApplication::translate("HzTestDemoClass", "\345\201\234\346\255\242\351\207\207\351\233\206", 0));
        stateTable->setTabText(stateTable->indexOf(videotab), QApplication::translate("HzTestDemoClass", "\350\247\206\351\242\221\351\207\207\351\233\206", 0));
        label_2->setText(QApplication::translate("HzTestDemoClass", "\351\200\232\351\201\223\347\261\273\345\236\213", 0));
        label_3->setText(QApplication::translate("HzTestDemoClass", "\346\230\257\345\220\246\345\255\230\345\202\250:", 0));
        label_6->setText(QApplication::translate("HzTestDemoClass", "\345\242\236\347\233\212\345\200\274", 0));
        chTypeCmb->clear();
        chTypeCmb->insertItems(0, QStringList()
         << QApplication::translate("HzTestDemoClass", "AD", 0)
         << QApplication::translate("HzTestDemoClass", "DDC", 0)
        );
        onOffCmb->clear();
        onOffCmb->insertItems(0, QStringList()
         << QApplication::translate("HzTestDemoClass", "OFF", 0)
         << QApplication::translate("HzTestDemoClass", "ON", 0)
        );
        label_5->setText(QApplication::translate("HzTestDemoClass", "\347\252\204\345\270\246\345\270\246\345\256\275", 0));
        label_4->setText(QApplication::translate("HzTestDemoClass", "\344\277\241\345\217\267\351\242\221\347\216\207", 0));
        sigFreqHzDsb->setSuffix(QApplication::translate("HzTestDemoClass", " MHz", 0));
        collectBtn_2->setText(QApplication::translate("HzTestDemoClass", "\344\270\213\345\217\221", 0));
        stateTable->setTabText(stateTable->indexOf(coltable), QApplication::translate("HzTestDemoClass", "\344\277\241\345\217\267\351\207\207\351\233\206", 0));
    } // retranslateUi

};

namespace Ui {
    class HzTestDemoClass: public Ui_HzTestDemoClass {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_HZTESTDEMO_H
