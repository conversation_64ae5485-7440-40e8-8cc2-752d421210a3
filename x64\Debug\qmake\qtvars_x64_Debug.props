<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
<Qt_DEFINES_>_WINDOWS;UNICODE;WIN32;WIN64;QT_PRINTSUPPORT_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB</Qt_DEFINES_>
<Qt_INCLUDEPATH_>C:\Users\<USER>\AppData\Local\Temp\c3zcj4ot.pvi;C:\Qt\Qt5.6.0\5.6\msvc2015_64\include;C:\Qt\Qt5.6.0\5.6\msvc2015_64\include\QtPrintSupport;C:\Qt\Qt5.6.0\5.6\msvc2015_64\include\QtWidgets;C:\Qt\Qt5.6.0\5.6\msvc2015_64\include\QtGui;C:\Qt\Qt5.6.0\5.6\msvc2015_64\include\QtANGLE;C:\Qt\Qt5.6.0\5.6\msvc2015_64\include\QtCore;C:\Users\<USER>\AppData\Local\Temp\c3zcj4ot.pvi;C:\Qt\Qt5.6.0\5.6\msvc2015_64\mkspecs\win32-msvc2015</Qt_INCLUDEPATH_>
<Qt_STDCPP_></Qt_STDCPP_>
<Qt_RUNTIME_>MultiThreadedDebugDLL</Qt_RUNTIME_>
<Qt_CL_OPTIONS_>-Zc:strictStrings -Zc:throwingNew</Qt_CL_OPTIONS_>
<Qt_LIBS_>C:\Qt\Qt5.6.0\5.6\msvc2015_64\lib\qtmaind.lib;shell32.lib;C:\Qt\Qt5.6.0\5.6\msvc2015_64\lib\Qt5PrintSupportd.lib;C:\Qt\Qt5.6.0\5.6\msvc2015_64\lib\Qt5Widgetsd.lib;C:\Qt\Qt5.6.0\5.6\msvc2015_64\lib\Qt5Guid.lib;C:\Qt\Qt5.6.0\5.6\msvc2015_64\lib\Qt5Cored.lib</Qt_LIBS_>
<Qt_LINK_OPTIONS_>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"</Qt_LINK_OPTIONS_>
<QMake_QT_SYSROOT_></QMake_QT_SYSROOT_>
<QMake_QT_INSTALL_PREFIX_>C:/Qt/Qt5.6.0/5.6/msvc2015_64</QMake_QT_INSTALL_PREFIX_>
<QMake_QT_INSTALL_ARCHDATA_>C:/Qt/Qt5.6.0/5.6/msvc2015_64</QMake_QT_INSTALL_ARCHDATA_>
<QMake_QT_INSTALL_DATA_>C:/Qt/Qt5.6.0/5.6/msvc2015_64</QMake_QT_INSTALL_DATA_>
<QMake_QT_INSTALL_DOCS_>C:/Qt/Qt5.6.0/Docs/Qt-5.6</QMake_QT_INSTALL_DOCS_>
<QMake_QT_INSTALL_HEADERS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/include</QMake_QT_INSTALL_HEADERS_>
<QMake_QT_INSTALL_LIBS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/lib</QMake_QT_INSTALL_LIBS_>
<QMake_QT_INSTALL_LIBEXECS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/bin</QMake_QT_INSTALL_LIBEXECS_>
<QMake_QT_INSTALL_BINS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/bin</QMake_QT_INSTALL_BINS_>
<QMake_QT_INSTALL_TESTS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/tests</QMake_QT_INSTALL_TESTS_>
<QMake_QT_INSTALL_PLUGINS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/plugins</QMake_QT_INSTALL_PLUGINS_>
<QMake_QT_INSTALL_IMPORTS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/imports</QMake_QT_INSTALL_IMPORTS_>
<QMake_QT_INSTALL_QML_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/qml</QMake_QT_INSTALL_QML_>
<QMake_QT_INSTALL_TRANSLATIONS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/translations</QMake_QT_INSTALL_TRANSLATIONS_>
<QMake_QT_INSTALL_CONFIGURATION_></QMake_QT_INSTALL_CONFIGURATION_>
<QMake_QT_INSTALL_EXAMPLES_>C:/Qt/Qt5.6.0/Examples/Qt-5.6</QMake_QT_INSTALL_EXAMPLES_>
<QMake_QT_INSTALL_DEMOS_>C:/Qt/Qt5.6.0/Examples/Qt-5.6</QMake_QT_INSTALL_DEMOS_>
<QMake_QT_HOST_PREFIX_>C:/Qt/Qt5.6.0/5.6/msvc2015_64</QMake_QT_HOST_PREFIX_>
<QMake_QT_HOST_DATA_>C:/Qt/Qt5.6.0/5.6/msvc2015_64</QMake_QT_HOST_DATA_>
<QMake_QT_HOST_BINS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/bin</QMake_QT_HOST_BINS_>
<QMake_QT_HOST_LIBS_>C:/Qt/Qt5.6.0/5.6/msvc2015_64/lib</QMake_QT_HOST_LIBS_>
<QMake_QMAKE_SPEC_>win32-msvc2015</QMake_QMAKE_SPEC_>
<QMake_QMAKE_XSPEC_>win32-msvc2015</QMake_QMAKE_XSPEC_>
<QMake_QMAKE_VERSION_>3.0</QMake_QMAKE_VERSION_>
<QMake_QT_VERSION_>5.6.0</QMake_QT_VERSION_>
<Qt_INCLUDEPATH_
      >$(Qt_INCLUDEPATH_);x64\Debug\moc;x64\Debug\uic</Qt_INCLUDEPATH_>
    <QtBkup_QtInstall
      >msvc2015_64</QtBkup_QtInstall>
    <QtBkup_QtModules
      >core;gui;widgets;printsupport</QtBkup_QtModules>
    <QtBkup_QtPathBinaries
      >bin</QtBkup_QtPathBinaries>
    <QtBkup_QtPathLibraryExecutables
      >bin</QtBkup_QtPathLibraryExecutables>
    <QtBkup_QtHeaderSearchPath
      ></QtBkup_QtHeaderSearchPath>
    <QtBkup_QtLibrarySearchPath
      ></QtBkup_QtLibrarySearchPath>
    <QtBkup_QtVars
      >DEFINES=/Project/ItemDefinitionGroup/ClCompile/PreprocessorDefinitions;INCLUDEPATH=/Project/ItemDefinitionGroup/ClCompile/AdditionalIncludeDirectories;STDCPP=/Project/ItemDefinitionGroup/ClCompile/LanguageStandard;RUNTIME=/Project/ItemDefinitionGroup/ClCompile/RuntimeLibrary;CL_OPTIONS=/Project/ItemDefinitionGroup/ClCompile/AdditionalOptions;LIBS=/Project/ItemDefinitionGroup/Link/AdditionalDependencies;LINK_OPTIONS=/Project/ItemDefinitionGroup/Link/AdditionalOptions</QtBkup_QtVars>
    <QtBkup_QMakeCodeLines
      ></QtBkup_QMakeCodeLines>
    <QtBkup_QtBuildConfig
      >debug</QtBkup_QtBuildConfig>
    <QtVersion>5.6.0</QtVersion>
    <QtVersionMajor>5</QtVersionMajor>
    <QtVersionMinor>6</QtVersionMinor>
    <QtVersionPatch>0</QtVersionPatch>
  </PropertyGroup>
</Project>
