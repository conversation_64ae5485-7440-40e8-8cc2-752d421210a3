QT_SYSROOT:
QT_INSTALL_PREFIX:C:/Qt/Qt5.6.0/5.6/msvc2015_64
QT_INSTALL_ARCHDATA:C:/Qt/Qt5.6.0/5.6/msvc2015_64
QT_INSTALL_DATA:C:/Qt/Qt5.6.0/5.6/msvc2015_64
QT_INSTALL_DOCS:C:/Qt/Qt5.6.0/Docs/Qt-5.6
QT_INSTALL_HEADERS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/include
QT_INSTALL_LIBS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/lib
QT_INSTALL_LIBEXECS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/bin
QT_INSTALL_BINS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/bin
QT_INSTALL_TESTS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/tests
QT_INSTALL_PLUGINS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/plugins
QT_INSTALL_IMPORTS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/imports
QT_INSTALL_QML:C:/Qt/Qt5.6.0/5.6/msvc2015_64/qml
QT_INSTALL_TRANSLATIONS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/translations
QT_INSTALL_CONFIGURATION:
QT_INSTALL_EXAMPLES:C:/Qt/Qt5.6.0/Examples/Qt-5.6
QT_INSTALL_DEMOS:C:/Qt/Qt5.6.0/Examples/Qt-5.6
QT_HOST_PREFIX:C:/Qt/Qt5.6.0/5.6/msvc2015_64
QT_HOST_DATA:C:/Qt/Qt5.6.0/5.6/msvc2015_64
QT_HOST_BINS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/bin
QT_HOST_LIBS:C:/Qt/Qt5.6.0/5.6/msvc2015_64/lib
QMAKE_SPEC:win32-msvc2015
QMAKE_XSPEC:win32-msvc2015
QMAKE_VERSION:3.0
QT_VERSION:5.6.0
