<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>HzTestDemoClass</class>
 <widget class="QDialog" name="HzTestDemoClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>651</width>
    <height>390</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>HzTestDemo</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2">
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="verticalLayout_3">
     <item>
      <widget class="QTabWidget" name="stateTable">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="currentIndex">
        <number>1</number>
       </property>
       <widget class="QWidget" name="videotab">
        <attribute name="title">
         <string>视频采集</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout" stretch="0">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QPushButton" name="videoStartBtn">
             <property name="text">
              <string>开始采集</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="videoStopBtn">
             <property name="text">
              <string>停止采集</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="coltable">
        <attribute name="title">
         <string>信号采集</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_2" stretch="0">
         <item>
          <layout class="QGridLayout" name="gridLayout">
           <item row="0" column="0">
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>通道类型</string>
             </property>
            </widget>
           </item>
           <item row="1" column="4">
            <widget class="QDoubleSpinBox" name="ddcBwDsb">
             <property name="decimals">
              <number>1</number>
             </property>
             <property name="minimum">
              <double>1.000000000000000</double>
             </property>
             <property name="maximum">
              <double>320.000000000000000</double>
             </property>
            </widget>
           </item>
           <item row="1" column="1" colspan="2">
            <widget class="QSpinBox" name="attSb">
             <property name="minimum">
              <number>0</number>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>是否存储:</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_6">
             <property name="text">
              <string>增益值</string>
             </property>
            </widget>
           </item>
           <item row="0" column="1" colspan="2">
            <widget class="QComboBox" name="chTypeCmb">
             <item>
              <property name="text">
               <string>AD</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>DDC</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="0" column="4">
            <widget class="QComboBox" name="onOffCmb">
             <item>
              <property name="text">
               <string>OFF</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>ON</string>
              </property>
             </item>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>窄带带宽</string>
             </property>
            </widget>
           </item>
           <item row="0" column="5">
            <widget class="QLabel" name="label_4">
             <property name="text">
              <string>信号频率</string>
             </property>
            </widget>
           </item>
           <item row="0" column="6">
            <widget class="QDoubleSpinBox" name="sigFreqHzDsb">
             <property name="suffix">
              <string> MHz</string>
             </property>
             <property name="decimals">
              <number>1</number>
             </property>
             <property name="minimum">
              <double>3.000000000000000</double>
             </property>
             <property name="maximum">
              <double>6000.000000000000000</double>
             </property>
            </widget>
           </item>
           <item row="1" column="6">
            <widget class="QPushButton" name="collectBtn_2">
             <property name="text">
              <string>下发</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </widget>
     </item>
     <item>
      <widget class="QTextBrowser" name="userinfotext"/>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="HzTestDemo.qrc"/>
 </resources>
 <connections/>
</ui>
