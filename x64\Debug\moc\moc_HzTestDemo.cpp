/****************************************************************************
** Meta object code from reading C++ file 'HzTestDemo.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.0)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "stdafx.h"
#include "../../../HzTestDemo.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'HzTestDemo.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.0. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_HzTestDemo_t {
    QByteArrayData data[7];
    char stringdata0[125];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_HzTestDemo_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_HzTestDemo_t qt_meta_stringdata_HzTestDemo = {
    {
QT_MOC_LITERAL(0, 0, 10), // "HzTestDemo"
QT_MOC_LITERAL(1, 11, 18), // "fft_repaint_signal"
QT_MOC_LITERAL(2, 30, 0), // ""
QT_MOC_LITERAL(3, 31, 24), // "on_videoStartBtn_clicked"
QT_MOC_LITERAL(4, 56, 23), // "on_videoStopBtn_clicked"
QT_MOC_LITERAL(5, 80, 21), // "on_collectBtn_clicked"
QT_MOC_LITERAL(6, 102, 22) // "on_fftClearBtn_clicked"

    },
    "HzTestDemo\0fft_repaint_signal\0\0"
    "on_videoStartBtn_clicked\0"
    "on_videoStopBtn_clicked\0on_collectBtn_clicked\0"
    "on_fftClearBtn_clicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_HzTestDemo[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   39,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       3,    0,   40,    2, 0x0a /* Public */,
       4,    0,   41,    2, 0x0a /* Public */,
       5,    0,   42,    2, 0x0a /* Public */,
       6,    0,   43,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void HzTestDemo::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        HzTestDemo *_t = static_cast<HzTestDemo *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->fft_repaint_signal(); break;
        case 1: _t->on_videoStartBtn_clicked(); break;
        case 2: _t->on_videoStopBtn_clicked(); break;
        case 3: _t->on_collectBtn_clicked(); break;
        case 4: _t->on_fftClearBtn_clicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (HzTestDemo::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&HzTestDemo::fft_repaint_signal)) {
                *result = 0;
                return;
            }
        }
    }
    Q_UNUSED(_a);
}

const QMetaObject HzTestDemo::staticMetaObject = {
    { &QDialog::staticMetaObject, qt_meta_stringdata_HzTestDemo.data,
      qt_meta_data_HzTestDemo,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *HzTestDemo::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *HzTestDemo::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_HzTestDemo.stringdata0))
        return static_cast<void*>(const_cast< HzTestDemo*>(this));
    return QDialog::qt_metacast(_clname);
}

int HzTestDemo::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void HzTestDemo::fft_repaint_signal()
{
    QMetaObject::activate(this, &staticMetaObject, 0, Q_NULLPTR);
}
QT_END_MOC_NAMESPACE
