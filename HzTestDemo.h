#pragma once

#include "ui_HzTestDemo.h"
#include <stdint.h>
#include <QCloseEvent>
struct http_server_s;
struct HttpService;
class HttpRequest;
class HttpResponse;

#pragma pack(push)
#pragma pack(1)
//colloct
typedef struct
{
	uint8_t chType;
	uint8_t onOff;
	double sigFreqHz;
	short att;
	double ddcBw;
} SignalCollect;
#pragma pack(pop)

#define GPS_DATA 0
#define EVENT_DATA 1
#define STATE_DATA 2
#define SELF_CHECK_DATA 3
#define MANUAL_DATA 4
class HzTestDemo;
typedef struct {
	HzTestDemo* _this;
	int flag;
}CbArg;

class HzTestDemo : public QDialog
{
    Q_OBJECT

public:
    HzTestDemo(QWidget *parent = Q_NULLPTR);
	~HzTestDemo();
	public slots:
	void on_videoStartBtn_clicked();
	void on_videoStopBtn_clicked();
	void on_collectBtn_clicked();
	void on_fftClearBtn_clicked();

public:
	int deal_video_json(QString jsonData);
	int deal_fft_json(QString jsonData);
protected:
	void closeEvent(QCloseEvent *e) override;
private:
	int recvDataDeal(int eventMode,HttpRequest* req, HttpResponse* res);
	void init_tcp_server(void);
signals:
	void fft_repaint_signal();
private:
    Ui::HzTestDemoClass ui;
private:
	HttpService * m_pService = nullptr;
	struct http_server_s * m_pServer = nullptr;
};
